import React, { useEffect, useRef } from 'react';
import { Animated, Image, Text, TouchableOpacity, View } from 'react-native';
import AntDesign from '@expo/vector-icons/AntDesign';
import Entypo from '@expo/vector-icons/Entypo';
import _map from 'lodash/map';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import StreakIcon from '@/assets/images/icons/Streak.png';
import WithNotificationBannerAnimationWeb from '../WithNotificationBannerAnimation';
import useStreakMaintainedOverlayStyles from './StreakMaintainedOverlay.style';
import useStreakAnalytics from '../../../modules/profile/hooks/query/useStreakAnalytics';
import { ANIMATION_DIRECTION } from '../WithNotificationBannerAnimation/animationConfig';
import { StreakMaintainedOverlayProps } from './types';

const StreakMaintainedOverlay: React.FC<StreakMaintainedOverlayProps> = (
  props,
) => {
  const styles = useStreakMaintainedOverlayStyles();
  const {
    showStreakMaintainedOverlay,
    payload,
    handleCloseStreakMaintainedOverlay,
  } = props;
  const waitingTime = 5;
  const { isMobile: isCompactMode } = useMediaQuery();
  const currentDayIndex = new Date(getCurrentTime()).getDay();
  const { weekDays, streakStatus, currentStreakCount } = useStreakAnalytics();
  const scaleAnimation = useRef(new Animated.Value(0)).current;

  const streakCountRef = useRef(currentStreakCount);
  streakCountRef.current = currentStreakCount;

  useEffect(() => {
    if (streakStatus[currentDayIndex]) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }).start();
    }
  }, [streakStatus, currentDayIndex]);

  if (!showStreakMaintainedOverlay) {
    return null;
  }

  return (
    <WithNotificationBannerAnimationWeb
      animationDuration={waitingTime}
      animationDirection={ANIMATION_DIRECTION.TTB}
      bannerStyle={{ top: 20, right: 0, width: '100%' }}
    >
      <View style={[styles.container, !isCompactMode && { maxWidth: 350 }]}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Image source={StreakIcon} style={styles.streakIcon} />
            <View
              style={{
                flexDirection: 'row',
                gap: 2,
                alignItems: 'flex-end',
                paddingHorizontal: 4,
              }}
            >
              <Text style={styles.daysCountText}>{currentStreakCount}</Text>
              <Text style={styles.daysText}>
                {currentStreakCount > 1 ? 'days' : 'day'} streak!
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={handleCloseStreakMaintainedOverlay}>
            <Entypo
              name="cross"
              size={18}
              color={dark.colors.textDark}
              onPress={handleCloseStreakMaintainedOverlay}
            />
          </TouchableOpacity>
        </View>
        <View style={styles.streakDaysContainer}>
          {_map(weekDays, (day, index) => (
            <View key={index} style={styles.streakDay}>
              {!streakStatus[index] ? (
                <View style={styles.streakUnactiveBox} />
              ) : index === currentDayIndex ? (
                <Animated.View
                  style={[
                    styles.streakUnactiveBox,
                    styles.streakActiveBox,
                    { transform: [{ scale: scaleAnimation }] },
                  ]}
                >
                  <AntDesign
                    name="check"
                    size={10}
                    color="black"
                    style={{ fontWeight: '800' }}
                  />
                </Animated.View>
              ) : (
                <View
                  style={[styles.streakUnactiveBox, styles.streakActiveBox]}
                >
                  <AntDesign
                    name="check"
                    size={10}
                    color="black"
                    style={{ fontWeight: '800' }}
                  />
                </View>
              )}
              <Text
                style={[
                  styles.dayText,
                  currentDayIndex === index && { color: dark.colors.secondary },
                ]}
              >
                {day}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </WithNotificationBannerAnimationWeb>
  );
};

export default React.memo(StreakMaintainedOverlay);
