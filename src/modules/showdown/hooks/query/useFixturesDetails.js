import { useQuery, gql } from '@apollo/client';
import { useMemo } from 'react';
import useRefetchOnAppFocus from 'core/hooks/useRefetchOnAppFocus';
import { useSession } from '../../../auth/containers/AuthProvider';
import useUserSubscription from '../../../../overlays/hooks/useUserEventsSubscription';

const FICTURES_DETAILS_QUERY = gql`
  query GetFicturesByShowdownId($showdownId: ID!) {
    getFicturesByShowdownId(showdownId: $showdownId) {
      currentUserFicture {
        showdownId
        users {
          showdownParticipant {
            _id
            userID
            rounds {
              opponent
              round
              score
              games
            }
            stats {
              currentScore
            }
            rank
            userInfo {
              name
              username
              profileImageUrl
              rating
            }
          }
          currentRound {
            opponent
            round
            score
            wins
          }
        }
        round
      }
    }
  }
`;

const useShowdownFixtures = ({ showdownId }) => {
  const { data, loading, error, refetch } = useQuery(FICTURES_DETAILS_QUERY, {
    variables: { showdownId },
    // fetchPolicy: 'cache-and-network',
  });
  const { userId } = useSession();

  const { event } = useUserSubscription(userId);

  useRefetchOnAppFocus(refetch);

  const fixtures = useMemo(() => {
    const _fixtures = data?.getFicturesByShowdownId;
    if (event === 'ShowdownFicturesCreated') {
      refetch();
    }
    return {
      ..._fixtures,
    };
  }, [data?.getFicturesByShowdownId, event, refetch]);

  return {
    loading,
    error,
    refetch,
    fixtures,
  };
};

export default useShowdownFixtures;
