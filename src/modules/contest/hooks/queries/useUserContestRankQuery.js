

import { gql, useQuery } from '@apollo/client'

const USER_RANK_IN_CONTEST = gql`
query GetUserContestResult($contestId: ID!) {
    getUserContestResult(contestId: $contestId) {
      totalScore
      startTime
      lastSubmissionTime
      correctSubmission
      incorrectSubmission
      rank
      questionsSolved
      totalParticipants
      isVirtualParticipant
      user {
        name
        profileImageUrl
        rating
        countryCode
        isGuest
        globalRank
        previousGlobalRank
        countryRank
        previousCountryRank
      }
    }
  }
`

const useGetUserRankInContestQuery = ({ contestId }) => {
    const { data, error, loading, refetch } = useQuery(
        USER_RANK_IN_CONTEST,
        {
            variables: {
                contestId
            },
        }
    )

    return {
        userRankInfo: data?.getUserContestResult,
        error,
        loading,
        refetch,
    }
}

export default useGetUserRankInContestQuery
