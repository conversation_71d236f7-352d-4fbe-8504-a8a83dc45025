import { useCallback, useRef, useState } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import useUpdateCurrentUserOnGameEnd from '../../modules/game/hooks/useUpdateCurrentUserOnGameEnd';

const useHandleStreakMaintainedEvent = () => {
  const [showStreakMaintainedOverlay, setShowStreakMaintainedOverlay] =
    useState(false);
  const { updateUserStreak } = useUpdateCurrentUserOnGameEnd();
  const updateStreakInCacheRef = useRef(updateUserStreak);
  updateStreakInCacheRef.current = updateUserStreak;

  const handleCloseOverlay = useCallback(({ payload }) => {
    setShowStreakMaintainedOverlay(false);
  }, []);

  const { user } = useSession();
  const currentStreak = userReader.currentStreak(user);

  const handleStreakMaintainedEvent = useCallback(
    ({ payload }) => {
      const { isPlayedToday } = payload;
      if (isPlayedToday) {
        Analytics.track(ANALYTICS_EVENTS.STREAKS.STREAK_UPDATED, {
          streakCount: currentStreak + 1,
        });
        updateStreakInCacheRef?.current();
        setShowStreakMaintainedOverlay(true);
        setTimeout(() => {
          setShowStreakMaintainedOverlay(false);
        }, 5000);
      }
    },
    [currentStreak],
  );

  return {
    showStreakMaintainedOverlay,
    handleCloseOverlay,
    handleStreakMaintainedEvent,
  };
};

export default useHandleStreakMaintainedEvent;
